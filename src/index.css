@tailwind base;
@tailwind components;
@tailwind utilities;

/* Twitter-inspired portfolio design system 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Twitter-inspired light theme */
    --background: 0 0% 100%;
    --foreground: 210 11% 15%;

    --card: 0 0% 100%;
    --card-foreground: 210 11% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 11% 15%;

    /* Twitter blue primary */
    --primary: 203 89% 53%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 203 89% 46%;
    
    /* Custom gradients */
    --gradient-primary: linear-gradient(135deg, hsl(203 89% 53%), hsl(203 89% 46%));
    --gradient-card: linear-gradient(145deg, hsl(var(--background)), hsl(var(--muted) / 0.3));

    /* Subtle grays */
    --secondary: 210 20% 96%;
    --secondary-foreground: 210 11% 15%;

    --muted: 210 20% 96%;
    --muted-foreground: 210 6% 46%;

    --accent: 210 20% 96%;
    --accent-foreground: 210 11% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Twitter-style borders */
    --border: 210 20% 90%;
    --input: 210 20% 90%;
    --ring: 203 89% 53%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Twitter dark theme */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 210 11% 3%;
    --card-foreground: 0 0% 100%;

    --popover: 210 11% 3%;
    --popover-foreground: 0 0% 100%;

    /* Twitter blue stays consistent */
    --primary: 203 89% 53%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 203 89% 46%;

    --secondary: 210 11% 9%;
    --secondary-foreground: 0 0% 100%;

    --muted: 210 11% 9%;
    --muted-foreground: 210 6% 56%;

    --accent: 210 11% 9%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 11% 9%;
    --input: 210 11% 9%;
    --ring: 203 89% 53%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}