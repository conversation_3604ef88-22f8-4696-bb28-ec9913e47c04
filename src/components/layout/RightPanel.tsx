import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { MapPin, Calendar, ExternalLink, Terminal, Send } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

const techStack = [
  "Python", "C++", "TypeScript", "JavaScript", "Dart", "Flutter",
  "Node.js", "NestJS", "LangChain", "Pandas", "NumPy", "PyTorch",
  "MongoDB", "MySQL", "Neo4j", "Redis", "Firebase", "Docker",
  "Git", "Google Cloud Platform", "N8N"
];

const featuredProject = {
  title: "Vibeo - AI-Powered Nightlife Discovery",
  description: "Co-founded and architected an AI-powered mobile app for personalized nightlife video discovery using Flutter, OpenAI Vision, and GCP.",
  tech: ["Flutter", "Python", "OpenAI Vision", "GCP", "Pinecone", "LangChain"],
  demoUrl: "https://www.vibeo.io"
};

export default function RightPanel() {
  const form = useForm({
    defaultValues: {
      email: "",
      message: ""
    }
  });

  const onSubmit = (data: any) => {
    console.log("Contact form submitted:", data);
    toast.success("Message sent! I'll get back to you soon.");
    form.reset();
  };

  return (
    <div className="hidden lg:block fixed right-0 top-0 h-full w-80 bg-background border-l border-border p-4 overflow-y-auto">
      {/* Profile Summary */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold text-xl font-mono">
              <Terminal className="h-8 w-8" />
            </div>
            <div>
              <h3 className="font-bold text-lg font-mono">~/pratik-dev</h3>
              <p className="text-muted-foreground font-mono text-sm">$ whoami</p>
              <p className="text-muted-foreground">Creative Technologist | AI/ML Engineer</p>
            </div>
          </div>
          
          <div className="bg-muted/30 rounded-md p-3 mb-4 font-mono text-xs">
            <p className="text-green-400 mb-1">$ cat about.txt</p>
            <p className="text-sm text-muted-foreground">
              MS Computer Science student at Illinois Institute of Technology specializing in AI/ML.
              Co-founder & CTO at Vibeo Inc. Passionate about building AI-powered solutions.
            </p>
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <MapPin className="h-4 w-4" />
              <span>Chicago, IL</span>
            </div>
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>Graduating May 2025</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Now Working On */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Now Working On</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/30 rounded-md p-3 font-mono text-xs">
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-green-400">●</span>
              <span className="text-sm font-medium font-mono">$ ps aux | grep current_project</span>
            </div>
            <p className="text-xs text-muted-foreground pl-4">
              → AI Agent Code Reviewer
            </p>
            <p className="text-xs text-muted-foreground pl-4">
              Building an AI agent that connects to repositories and enables conversational code review via LLM chat interface.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Tech Stack */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Tech Stack</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {techStack.map((tech) => (
              <Badge 
                key={tech} 
                variant="secondary" 
                className="hover:bg-primary hover:text-primary-foreground transition-colors cursor-default"
              >
                {tech}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Featured Project */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Featured Project</CardTitle>
        </CardHeader>
        <CardContent>
          <h4 className="font-semibold mb-2">{featuredProject.title}</h4>
          <p className="text-sm text-muted-foreground mb-3">
            {featuredProject.description}
          </p>
          
          <div className="flex flex-wrap gap-1 mb-3">
            {featuredProject.tech.map((tech) => (
              <Badge key={tech} variant="outline" className="text-xs">
                {tech}
              </Badge>
            ))}
          </div>
          
          <Button size="sm" className="w-full">
            <ExternalLink className="h-4 w-4 mr-2" />
            View Project
          </Button>
        </CardContent>
      </Card>

      {/* Quick Contact */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-mono">$ send_message</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/30 rounded-md p-3 mb-4 font-mono text-xs">
            <p className="text-green-400 mb-2">// Drop me a line!</p>
            <p className="text-muted-foreground">
              Interested in AI/ML projects or collaboration? Let's connect! Email: <EMAIL>
            </p>
          </div>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-mono text-xs">email:</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="<EMAIL>" 
                        type="email"
                        className="font-mono text-sm"
                        required
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-mono text-xs">message:</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Hello! I'd like to connect..."
                        className="min-h-[80px] font-mono text-sm resize-none"
                        required
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <Button type="submit" className="w-full font-mono">
                <Send className="h-4 w-4 mr-2" />
                send --now
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}