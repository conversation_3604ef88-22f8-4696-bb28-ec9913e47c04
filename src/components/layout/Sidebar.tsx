import { Home, User, Briefcase, BookOpen, FileText, Mail, Github, Linkedin, Sun, Moon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

const navigation = [
  { name: "Home", icon: Home, href: "#home" },
  { name: "About", icon: User, href: "#about" },
  { name: "Projects", icon: Briefcase, href: "#projects" },
  { name: "Blog", icon: BookOpen, href: "#blog" },
  { name: "Resume", icon: FileText, href: "#resume" },
  { name: "Contact", icon: Mail, href: "#contact" },
];

const socialLinks = [
  { name: "GitHub", icon: Github, href: "https://github.com" },
  { name: "LinkedIn", icon: Linkedin, href: "https://linkedin.com" },
];

export default function Sidebar() {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const theme = localStorage.getItem('theme');
    if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      setIsDark(true);
      document.documentElement.classList.add('dark');
    }
  }, []);

  const toggleTheme = () => {
    const newTheme = !isDark;
    setIsDark(newTheme);
    if (newTheme) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  return (
    <div className="hidden lg:flex fixed left-0 top-0 h-full w-64 bg-background border-r border-border flex-col p-4">
      {/* Logo */}
      <div className="mb-8">
        <h1 className="text-xl font-bold text-foreground">Portfolio</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-2">
        {navigation.map((item) => (
          <a
            key={item.name}
            href={item.href}
            className="flex items-center space-x-3 px-3 py-3 rounded-full text-foreground hover:bg-muted transition-colors duration-200 group"
          >
            <item.icon className="h-6 w-6 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-xl font-normal">{item.name}</span>
          </a>
        ))}
      </nav>

      {/* Social Links */}
      <div className="space-y-2 mb-4">
        {socialLinks.map((link) => (
          <a
            key={link.name}
            href={link.href}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-3 px-3 py-3 rounded-full text-muted-foreground hover:text-foreground hover:bg-muted transition-colors duration-200 group"
          >
            <link.icon className="h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-lg">{link.name}</span>
          </a>
        ))}
      </div>

      {/* Theme Toggle */}
      <Button
        variant="outline"
        size="sm"
        onClick={toggleTheme}
        className="flex items-center space-x-2 self-start"
      >
        {isDark ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
        <span>{isDark ? 'Light' : 'Dark'}</span>
      </Button>
    </div>
  );
}