import { useState } from "react";
import { Menu, X, Home, User, Briefcase, BookOpen, FileText, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";

const navigation = [
  { name: "Home", icon: Home, href: "#home" },
  { name: "About", icon: User, href: "#about" },
  { name: "Projects", icon: Briefcase, href: "#projects" },
  { name: "Blog", icon: BookOpen, href: "#blog" },
  { name: "Resume", icon: FileText, href: "#resume" },
  { name: "Contact", icon: Mail, href: "#contact" },
];

export default function MobileNav() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="lg:hidden">
      {/* Mobile Header */}
      <header className="fixed top-0 left-0 right-0 bg-background/95 backdrop-blur-sm border-b border-border z-50 p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold">Portfolio</h1>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            className="p-2"
          >
            {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="fixed inset-0 bg-black/50" onClick={() => setIsOpen(false)} />
          <div className="fixed left-0 top-0 h-full w-80 bg-background border-r border-border p-4">
            <div className="flex items-center justify-between mb-8">
              <h1 className="text-xl font-bold">Portfolio</h1>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="p-2"
              >
                <X className="h-6 w-6" />
              </Button>
            </div>
            
            <nav className="space-y-2">
              {navigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsOpen(false)}
                  className="flex items-center space-x-3 px-3 py-3 rounded-full text-foreground hover:bg-muted transition-colors duration-200"
                >
                  <item.icon className="h-6 w-6" />
                  <span className="text-xl font-normal">{item.name}</span>
                </a>
              ))}
            </nav>
          </div>
        </div>
      )}
    </div>
  );
}