import ProjectCard from "./ProjectCard";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import heroImage from "@/assets/hero-bg.jpg";

const sampleProjects = [
  {
    id: "1",
    title: "Vibeo - AI-Powered Nightlife Discovery",
    description: "Co-founded and architected an AI-powered mobile app for personalized nightlife video discovery. Engineered pipeline to handle 50+ daily video uploads with real-time analysis using OpenAI Vision Model, Python, GCP Cloud Functions, and Pinecone for vector-based retrieval. Scaled backend to support 25 concurrent users with <100ms response time.",
    image: heroImage,
    tech: ["Flutter", "Python", "OpenAI Vision", "GCP", "Pinecone", "LangChain", "Docker"],
    githubUrl: "https://github.com/PratikJH153",
    demoUrl: "https://www.vibeo.io",
    likes: 127,
    comments: 23,
    timeAgo: "Current"
  },
  {
    id: "2",
    title: "AI Agent - Code Reviewer",
    description: "Built an AI agent that connects to repositories, parses codebase structure, and understands project context through file analysis. Enabled conversational code review by allowing users to query commit history, merges, and proposed changes via LLM chat interface.",
    image: heroImage,
    tech: ["Python", "LangChain", "OpenAI API", "Git", "Vector DB"],
    githubUrl: "https://github.com/PratikJH153",
    demoUrl: "#",
    likes: 89,
    comments: 15,
    timeAgo: "2 months"
  },
  {
    id: "3",
    title: "Enthem - Social Media App",
    description: "Developed platform enabling users to connect with like-minded peers nearby using Neo4j's proximity-based graph clustering. Implemented features for creating and joining temporary, hyper-personalized chat rooms based on shared interests and location.",
    image: heroImage,
    tech: ["React", "Node.js", "Neo4j", "WebSocket", "MongoDB"],
    githubUrl: "https://github.com/PratikJH153",
    likes: 64,
    comments: 12,
    timeAgo: "4 months"
  },
  {
    id: "4",
    title: "Wajooba LLC - Backend Development",
    description: "Constructed and improved backend features using Node.js, MongoDB, and Neo4j, making data processing 20% faster and enabling users to generate detailed, customized reports. Integrated tools like Airbyte for data syncing and SendGrid for email communication. Improved system performance by 15% through containerization.",
    image: heroImage,
    tech: ["Node.js", "MongoDB", "Neo4j", "Airbyte", "SendGrid", "Docker"],
    githubUrl: "https://github.com/PratikJH153",
    likes: 45,
    comments: 8,
    timeAgo: "1 year"
  }
];

const pinnedProject = {
  id: "pinned",
  title: "ShapeShift Conference - RAG AI Professor",
  description: "Facilitated an RAG AI 'live professor' concept with 3D avatar technology in physical space to reimagine virtual learning. Demonstrated work to leading designers from top multinational firms, receiving recognition for innovation in immersive education. Collaborated with Steelcase Inc. to prototype AI integration in 3D interactive spaces.",
  image: heroImage,
  tech: ["Python", "RAG", "3D Graphics", "AI/ML", "Unity", "LangChain"],
  githubUrl: "https://github.com/PratikJH153",
  demoUrl: "https://pratikjh.netlify.app",
  likes: 156,
  comments: 31,
  timeAgo: "Recent"
};

export default function MainFeed() {
  return (
    <div className="flex-1 max-w-2xl mx-auto">
      {/* Header */}
      <div className="sticky top-0 lg:top-0 bg-background/80 backdrop-blur-sm border-b border-border p-4 z-10">
        <h2 className="text-xl font-bold flex items-center space-x-2">
          <Sparkles className="h-6 w-6 text-primary" />
          <span>Projects & Work</span>
        </h2>
        <p className="text-muted-foreground text-sm mt-1">Latest projects and technical experiments</p>
      </div>

      {/* Pinned Project */}
      <div className="p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Pin className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium text-primary">Pinned Project</span>
        </div>
        <ProjectCard project={pinnedProject} />
      </div>

      {/* What's New Section */}
      <Card className="mx-4 mb-6">
        <CardContent className="p-6">
          <div className="flex items-start space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold text-sm">
              PJ
            </div>
            <div className="flex-1">
              <textarea
                placeholder="What AI/ML project are you working on today?"
                className="w-full bg-transparent text-xl placeholder-muted-foreground border-none outline-none resize-none"
                rows={3}
              />
              <div className="flex justify-between items-center mt-4">
                <div className="text-sm text-muted-foreground">
                  Share your latest AI innovation or technical breakthrough
                </div>
                <Button disabled className="opacity-50">
                  Share Project
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Projects Feed */}
      <div className="px-4">
        {sampleProjects.map((project) => (
          <ProjectCard key={project.id} project={project} />
        ))}
      </div>

      {/* Load More */}
      <div className="p-4 text-center">
        <Button variant="outline" className="w-full">
          Load More Projects
        </Button>
      </div>
    </div>
  );
}