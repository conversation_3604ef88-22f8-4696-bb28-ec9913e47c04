import ProjectCard from "./ProjectCard";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import heroImage from "@/assets/hero-bg.jpg";

const sampleProjects = [
  {
    id: "1",
    title: "AI-Powered Task Manager",
    description: "Built a smart task management app using React, Node.js, and OpenAI's API. Features include natural language task creation, intelligent scheduling, and productivity insights. The app learns from user behavior to suggest optimal work patterns.",
    image: heroImage,
    tech: ["React", "TypeScript", "Node.js", "OpenAI API", "PostgreSQL"],
    githubUrl: "https://github.com",
    demoUrl: "https://demo.com",
    likes: 42,
    comments: 8,
    timeAgo: "2h"
  },
  {
    id: "2", 
    title: "E-commerce Analytics Dashboard",
    description: "Developed a comprehensive analytics platform for e-commerce businesses. Real-time sales tracking, customer behavior analysis, and predictive analytics powered by machine learning algorithms.",
    image: heroImage,
    tech: ["Next.js", "D3.js", "Python", "TensorFlow", "AWS"],
    githubUrl: "https://github.com",
    demoUrl: "https://demo.com",
    likes: 28,
    comments: 12,
    timeAgo: "1d"
  },
  {
    id: "3",
    title: "Real-time Collaboration Tool",
    description: "Created a Slack-inspired collaboration platform with real-time messaging, file sharing, and video calls. Features include end-to-end encryption and seamless mobile experience.",
    image: heroImage,
    tech: ["React", "Socket.io", "WebRTC", "MongoDB", "Docker"],
    githubUrl: "https://github.com",
    likes: 35,
    comments: 6,
    timeAgo: "3d"
  }
];

const pinnedProject = {
  id: "pinned",
  title: "Portfolio Website (This Site!)",
  description: "A Twitter-inspired portfolio website built with React, TypeScript, and Tailwind CSS. Features a responsive layout, dark mode, and smooth animations. This project showcases my ability to recreate complex UI patterns while adding my own creative touch.",
  image: heroImage,
  tech: ["React", "TypeScript", "Tailwind CSS", "Vite"],
  githubUrl: "https://github.com",
  demoUrl: "https://portfolio.com",
  likes: 127,
  comments: 23,
  timeAgo: "1w"
};

export default function MainFeed() {
  return (
    <div className="flex-1 max-w-2xl mx-auto">
      {/* Header */}
      <div className="sticky top-0 lg:top-0 bg-background/80 backdrop-blur-sm border-b border-border p-4 z-10">
        <h2 className="text-xl font-bold flex items-center space-x-2">
          <Sparkles className="h-6 w-6 text-primary" />
          <span>Projects & Work</span>
        </h2>
        <p className="text-muted-foreground text-sm mt-1">Latest projects and technical experiments</p>
      </div>

      {/* Pinned Project */}
      <div className="p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Pin className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium text-primary">Pinned Project</span>
        </div>
        <ProjectCard project={pinnedProject} />
      </div>

      {/* What's New Section */}
      <Card className="mx-4 mb-6">
        <CardContent className="p-6">
          <div className="flex items-start space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold text-sm">
              JD
            </div>
            <div className="flex-1">
              <textarea 
                placeholder="What are you building today?"
                className="w-full bg-transparent text-xl placeholder-muted-foreground border-none outline-none resize-none"
                rows={3}
              />
              <div className="flex justify-between items-center mt-4">
                <div className="text-sm text-muted-foreground">
                  Share your latest project or tech discovery
                </div>
                <Button disabled className="opacity-50">
                  Share Project
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Projects Feed */}
      <div className="px-4">
        {sampleProjects.map((project) => (
          <ProjectCard key={project.id} project={project} />
        ))}
      </div>

      {/* Load More */}
      <div className="p-4 text-center">
        <Button variant="outline" className="w-full">
          Load More Projects
        </Button>
      </div>
    </div>
  );
}