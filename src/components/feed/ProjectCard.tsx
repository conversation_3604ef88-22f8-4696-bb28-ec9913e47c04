import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Heart, MessageCircle, Share, ExternalLink, Github } from "lucide-react";
import { useState } from "react";

interface ProjectCardProps {
  project: {
    id: string;
    title: string;
    description: string;
    image: string;
    tech: string[];
    githubUrl?: string;
    demoUrl?: string;
    likes: number;
    comments: number;
    timeAgo: string;
  };
}

export default function ProjectCard({ project }: ProjectCardProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(project.likes);

  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
  };

  return (
    <Card className="mb-4 hover:shadow-lg transition-shadow duration-200 cursor-pointer group">
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-start space-x-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/60 rounded-full flex items-center justify-center text-white font-bold text-sm">
            JD
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <span className="font-semibold">John Developer</span>
              <span className="text-muted-foreground">·</span>
              <span className="text-muted-foreground text-sm">{project.timeAgo}</span>
            </div>
            <p className="text-muted-foreground text-sm">@johndeveloper</p>
          </div>
        </div>

        {/* Content */}
        <div className="mb-4">
          <h3 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors">
            {project.title}
          </h3>
          <p className="text-foreground mb-4">{project.description}</p>
          
          {/* Tech Stack */}
          <div className="flex flex-wrap gap-2 mb-4">
            {project.tech.map((tech) => (
              <Badge key={tech} variant="secondary" className="text-xs">
                {tech}
              </Badge>
            ))}
          </div>

          {/* Project Image */}
          {project.image && (
            <div className="rounded-lg overflow-hidden mb-4 border border-border">
              <img 
                src={project.image} 
                alt={project.title}
                className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2 mb-4">
            {project.demoUrl && (
              <Button size="sm" className="flex items-center space-x-2">
                <ExternalLink className="h-4 w-4" />
                <span>Live Demo</span>
              </Button>
            )}
            {project.githubUrl && (
              <Button size="sm" variant="outline" className="flex items-center space-x-2">
                <Github className="h-4 w-4" />
                <span>Code</span>
              </Button>
            )}
          </div>
        </div>

        {/* Engagement Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-border">
          <div className="flex space-x-6">
            <button 
              className={`flex items-center space-x-2 text-sm transition-colors hover:text-primary ${
                isLiked ? 'text-red-500' : 'text-muted-foreground'
              }`}
              onClick={handleLike}
            >
              <Heart className={`h-5 w-5 ${isLiked ? 'fill-current' : ''}`} />
              <span>{likeCount}</span>
            </button>
            
            <button className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-colors">
              <MessageCircle className="h-5 w-5" />
              <span>{project.comments}</span>
            </button>
            
            <button className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-colors">
              <Share className="h-5 w-5" />
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}