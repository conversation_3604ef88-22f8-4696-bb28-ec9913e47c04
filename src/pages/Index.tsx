import Sidebar from "@/components/layout/Sidebar";
import RightPanel from "@/components/layout/RightPanel";
import MainFeed from "@/components/feed/MainFeed";
import MobileNav from "@/components/layout/MobileNav";

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      {/* Mobile Navigation */}
      <MobileNav />
      
      {/* Desktop Layout */}
      <div className="hidden lg:flex">
        {/* Left Sidebar - Navigation */}
        <Sidebar />
        
        {/* Main Content Area */}
        <div className="flex-1 ml-64 mr-80">
          <MainFeed />
        </div>
        
        {/* Right Panel - Info & Widgets */}
        <RightPanel />
      </div>
      
      {/* Mobile Layout */}
      <div className="lg:hidden pt-20">
        <div className="max-w-2xl mx-auto px-4">
          <MainFeed />
        </div>
      </div>
    </div>
  );
};

export default Index;
